import { useEffect, useState, useCallback, useRef } from 'react';
import {
  useImageStore,
  initializeImageStorage,
  loadPersistedImages,
} from '@/lib/store/imageStore';
import { imageStorage } from '@/lib/storage/indexeddb-storage';
import type { UploadedImage } from '../../background-remover/components/BackgroundImagePicker';

/**
 * 批量图片存储管理Hook
 * 专门为批量编辑器提供IndexedDB存储功能
 * 参考useImageStorage的实现，但针对批量处理场景进行优化
 */
export const useBatchImageStorage = () => {
  const [initializationStatus, setInitializationStatus] = useState<
    'idle' | 'initializing' | 'success' | 'error'
  >('idle');
  const [initializationError, setInitializationError] = useState<string | null>(
    null
  );

  // 从store获取存储相关状态
  const isStorageInitialized = useImageStore(s => s.isStorageInitialized);
  const isStorageLoading = useImageStore(s => s.isStorageLoading);
  const isSaving = useImageStore(s => s.isSaving);
  const autoSaveEnabled = useImageStore(s => s.autoSaveEnabled);

  // 获取存储相关方法
  const { cleanupOldImages, saveAllImagesToPersistentStorage } =
    useImageStore.getState();

  // 自定义背景图片状态
  const [uploadedBackgroundImages, setUploadedBackgroundImages] = useState<
    UploadedImage[]
  >([]);
  const [isLoadingBackgroundImages, setIsLoadingBackgroundImages] =
    useState(true);

  // 防止重复加载的标志
  const isLoadingRef = useRef(false);

  /**
   * 初始化存储并加载持久化数据
   */
  const initializeAndLoadStorage = useCallback(async () => {
    // 检查全局存储状态
    const globalStorageInitialized =
      useImageStore.getState().isStorageInitialized;

    console.log('批量编辑器初始化检查:', {
      localInitializationStatus: initializationStatus,
      globalStorageInitialized,
      localStorageInitialized: isStorageInitialized,
    });

    // 如果全局存储已经初始化，直接设置本地状态为成功
    if (globalStorageInitialized && initializationStatus !== 'success') {
      console.log('批量编辑器检测到全局存储已初始化，直接设置为成功状态');
      setInitializationStatus('success');
      return;
    }

    if (
      initializationStatus === 'initializing' ||
      initializationStatus === 'success'
    ) {
      return;
    }

    try {
      setInitializationStatus('initializing');
      setInitializationError(null);

      // 步骤1: 初始化IndexedDB（如果尚未初始化）
      const storageInitialized = await initializeImageStorage();
      if (!storageInitialized) {
        throw new Error('存储初始化失败');
      }

      // 步骤2: 加载持久化的图片
      const imagesLoaded = await loadPersistedImages();
      if (!imagesLoaded) {
        console.warn('加载持久化图片失败，但存储初始化成功');
      }

      setInitializationStatus('success');
    } catch (error) {
      console.error('批量编辑器存储初始化失败:', error);
      setInitializationError(
        error instanceof Error ? error.message : '未知错误'
      );
      setInitializationStatus('error');
    }
  }, []); // 移除依赖，避免无限循环

  /**
   * 保存所有图片到持久化存储
   */
  const saveAllImages = useCallback(async () => {
    if (!isStorageInitialized || !autoSaveEnabled) return;
    await saveAllImagesToPersistentStorage();
  }, [isStorageInitialized, autoSaveEnabled, saveAllImagesToPersistentStorage]);

  /**
   * 清理存储空间
   */
  const cleanupStorage = useCallback(async () => {
    if (!isStorageInitialized) return;
    await cleanupOldImages();
  }, [isStorageInitialized, cleanupOldImages]);

  // 组件挂载时初始化存储
  useEffect(() => {
    initializeAndLoadStorage();
  }, []); // 移除initializeAndLoadStorage依赖，避免无限循环

  // 加载自定义背景图片
  const loadBackgroundImages = useCallback(async () => {
    if (!isStorageInitialized) {
      console.log('跳过背景图片加载: 存储未初始化');
      return;
    }

    // 防止重复加载
    if (isLoadingRef.current) {
      console.log('跳过背景图片加载: 正在加载中');
      return;
    }

    try {
      console.log('批量编辑器开始加载背景图片...');
      isLoadingRef.current = true;
      setIsLoadingBackgroundImages(true);

      const allBackgroundImages = await imageStorage.getAllBackgroundImages();
      console.log(
        `批量编辑器从IndexedDB获取到 ${allBackgroundImages.length} 个背景图片元数据`
      );

      const loadedImages: UploadedImage[] = [];

      for (const metadata of allBackgroundImages) {
        try {
          const loadResult = await imageStorage.loadBackgroundImage(
            metadata.id
          );
          if (loadResult) {
            const uploadedImage: UploadedImage = {
              id: metadata.id,
              url: loadResult.url,
              name: metadata.name,
              timestamp: metadata.timestamp,
              file: loadResult.file,
            };
            loadedImages.push(uploadedImage);
          }
        } catch (error) {
          console.error(`加载背景图片失败: ${metadata.id}`, error);
        }
      }

      console.log(`批量编辑器成功加载 ${loadedImages.length} 个背景图片`);
      setUploadedBackgroundImages(loadedImages);
    } catch (error) {
      console.error('批量编辑器加载背景图片失败:', error);
    } finally {
      isLoadingRef.current = false;
      setIsLoadingBackgroundImages(false);
    }
  }, [isStorageInitialized]);

  // 在存储初始化完成后加载背景图片
  useEffect(() => {
    console.log('批量编辑器存储状态变化:', {
      isStorageInitialized,
      initializationStatus,
    });
    if (isStorageInitialized && initializationStatus === 'success') {
      console.log('批量编辑器触发背景图片加载');
      loadBackgroundImages();
    }
  }, [isStorageInitialized, initializationStatus]); // 移除loadBackgroundImages依赖，避免无限循环

  // 保存背景图片到IndexedDB
  const saveBackgroundImage = async (image: UploadedImage) => {
    console.log('批量编辑器saveBackgroundImage调用:', {
      isStorageInitialized,
      hasFile: !!image.file,
      imageId: image.id,
    });

    if (!isStorageInitialized) {
      console.error('批量编辑器存储未初始化，无法保存背景图片');
      return;
    }

    if (!image.file) {
      console.error('批量编辑器背景图片文件为空，无法保存');
      return;
    }

    try {
      console.log('批量编辑器开始调用imageStorage.saveBackgroundImage');
      await imageStorage.saveBackgroundImage(image.id, image.file);
      console.log(`批量编辑器背景图片保存成功: ${image.id}`);
    } catch (error) {
      console.error(`批量编辑器保存背景图片失败: ${image.id}`, error);
      throw error;
    }
  };

  // 删除自定义背景图片
  const deleteBackgroundImage = async (imageId: string) => {
    if (!isStorageInitialized) return;

    try {
      // 先找到要删除的图片
      let imageToDelete: UploadedImage | undefined;

      setUploadedBackgroundImages(prev => {
        imageToDelete = prev.find(img => img.id === imageId);
        return prev.filter(img => img.id !== imageId);
      });

      // 清理 URL
      if (imageToDelete?.url.startsWith('blob:')) {
        URL.revokeObjectURL(imageToDelete.url);
      }

      // 从 IndexedDB 中删除
      await imageStorage.deleteBackgroundImage(imageId);
      console.log(`批量编辑器背景图片删除成功: ${imageId}`);
    } catch (error) {
      console.error(`批量编辑器删除背景图片失败: ${imageId}`, error);
      // 如果删除失败，重新加载以恢复状态
      loadBackgroundImages();
    }
  };

  // 添加新的背景图片
  const addBackgroundImage = async (file: File) => {
    console.log(
      '批量编辑器开始添加背景图片:',
      file.name,
      '存储初始化状态:',
      isStorageInitialized
    );

    const newImage: UploadedImage = {
      id: `bgimg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      url: URL.createObjectURL(file),
      name: file.name,
      timestamp: Date.now(),
      file: file,
    };

    console.log('批量编辑器创建新背景图片对象:', newImage.id);

    // 立即更新状态
    setUploadedBackgroundImages(prev => {
      console.log(
        '批量编辑器更新背景图片状态，当前数量:',
        prev.length,
        '新增后:',
        prev.length + 1
      );
      return [newImage, ...prev];
    });

    // 异步保存到 IndexedDB
    if (isStorageInitialized) {
      try {
        console.log('批量编辑器开始保存背景图片到IndexedDB:', newImage.id);
        await saveBackgroundImage(newImage);
        console.log('批量编辑器背景图片保存到IndexedDB成功:', newImage.id);
      } catch (error) {
        console.error('批量编辑器保存背景图片失败:', error);
        // 如果保存失败，从状态中移除
        setUploadedBackgroundImages(prev =>
          prev.filter(img => img.id !== newImage.id)
        );
        URL.revokeObjectURL(newImage.url);
      }
    } else {
      console.warn('批量编辑器存储未初始化，背景图片未保存到IndexedDB');
    }

    return newImage;
  };

  return {
    // 初始化状态
    initializationStatus,
    initializationError,
    isStorageInitialized,

    // 存储状态
    isStorageLoading,
    isSaving,
    autoSaveEnabled,

    // 操作方法
    initializeAndLoadStorage,
    saveAllImages,
    cleanupStorage,

    // 背景图片相关
    uploadedBackgroundImages,
    isLoadingBackgroundImages,
    addBackgroundImage,
    deleteBackgroundImage,
    loadBackgroundImages,
  };
};
